import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/semantics.dart';
import '../../theme/design_tokens.dart';
import '../../theme/semantic_colors.dart';

/// Enhanced focus traversal group that provides comprehensive keyboard navigation
/// for complex layouts with accessibility features, focus management, and debugging tools.
class AccessibleFocusTraversalGroup extends StatefulWidget {
  const AccessibleFocusTraversalGroup({
    super.key,
    required this.child,
    this.policy,
    this.descendantsAreFocusable = true,
    this.descendantsAreTraversable = true,
    this.skipTraversal = false,
    this.enableDirectionalNavigation = true,
    this.enableFocusTrapping = false,
    this.enableSkipLinks = true,
    this.enableFocusRestoration = true,
    this.enableAutoScroll = true,
    this.enableAccessibilityAnnouncements = true,
    this.enableFocusDebugging = kDebugMode,
    this.focusOrder,
    this.skipLinkTargets,
    this.onFocusChange,
    this.onFocusTrapped,
    this.onSkipLinkActivated,
    this.semanticLabel,
    this.landmarkRole,
    this.groupId,
    this.restoreKey,
    this.debugLabel,
  });

  /// The widget below this widget in the tree
  final Widget child;

  /// The policy used to determine the order of focus traversal
  final FocusTraversalPolicy? policy;

  /// Whether the descendants of this group can receive focus
  final bool descendantsAreFocusable;

  /// Whether the descendants of this group can be traversed
  final bool descendantsAreTraversable;

  /// Whether to skip this group during traversal
  final bool skipTraversal;

  /// Whether to enable directional navigation with arrow keys
  final bool enableDirectionalNavigation;

  /// Whether to trap focus within this group (useful for modals)
  final bool enableFocusTrapping;

  /// Whether to enable skip links for efficient navigation
  final bool enableSkipLinks;

  /// Whether to restore focus when returning to this group
  final bool enableFocusRestoration;

  /// Whether to automatically scroll focused elements into view
  final bool enableAutoScroll;

  /// Whether to announce focus changes to screen readers
  final bool enableAccessibilityAnnouncements;

  /// Whether to show focus debugging information
  final bool enableFocusDebugging;

  /// Manual focus order definition (overrides automatic ordering)
  final List<GlobalKey>? focusOrder;

  /// Skip link targets for efficient navigation
  final Map<String, GlobalKey>? skipLinkTargets;

  /// Callback when focus changes within this group
  final ValueChanged<FocusNode?>? onFocusChange;

  /// Callback when focus is trapped (for modal handling)
  final VoidCallback? onFocusTrapped;

  /// Callback when a skip link is activated
  final ValueChanged<String>? onSkipLinkActivated;

  /// Semantic label for screen readers
  final String? semanticLabel;

  /// Landmark role for screen reader navigation
  final String? landmarkRole;

  /// Unique identifier for this group
  final String? groupId;

  /// Key for focus restoration
  final String? restoreKey;

  /// Debug label for development
  final String? debugLabel;

  @override
  State<AccessibleFocusTraversalGroup> createState() =>
      _AccessibleFocusTraversalGroupState();
}

class _AccessibleFocusTraversalGroupState
    extends State<AccessibleFocusTraversalGroup> {
  late FocusScopeNode _groupFocusNode;
  late FocusNode _keyboardListenerFocusNode;
  FocusNode? _lastFocusedNode;
  final Map<String, FocusNode> _skipLinkNodes = {};
  final List<FocusNode> _focusHistory = [];
  bool _isTrapped = false;

  @override
  void initState() {
    super.initState();
    _groupFocusNode = FocusScopeNode(
      debugLabel: widget.debugLabel ?? 'AccessibleFocusTraversalGroup',
    );
    _groupFocusNode.addListener(_onGroupFocusChange);

    _keyboardListenerFocusNode = FocusNode(
      skipTraversal: true,
      debugLabel: 'KeyboardListener_${widget.debugLabel ?? 'AccessibleFocusTraversalGroup'}',
    );
    
    // Initialize skip link nodes
    if (widget.enableSkipLinks && widget.skipLinkTargets != null) {
      for (final entry in widget.skipLinkTargets!.entries) {
        _skipLinkNodes[entry.key] = FocusNode(
          debugLabel: 'SkipLink_${entry.key}',
        );
      }
    }

    // Restore focus if enabled
    if (widget.enableFocusRestoration && widget.restoreKey != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _restoreFocus();
      });
    }
  }

  @override
  void dispose() {
    _groupFocusNode.removeListener(_onGroupFocusChange);
    _groupFocusNode.dispose();
    _keyboardListenerFocusNode.dispose();

    for (final node in _skipLinkNodes.values) {
      node.dispose();
    }

    super.dispose();
  }

  void _onGroupFocusChange() {
    final currentFocus = FocusScope.of(context).focusedChild;
    
    if (currentFocus != _lastFocusedNode) {
      _lastFocusedNode = currentFocus;
      
      // Add to focus history
      if (currentFocus != null && !_focusHistory.contains(currentFocus)) {
        _focusHistory.add(currentFocus);
        if (_focusHistory.length > 10) {
          _focusHistory.removeAt(0);
        }
      }
      
      // Store focus for restoration
      if (widget.enableFocusRestoration && widget.restoreKey != null) {
        _storeFocus(currentFocus);
      }
      
      // Auto-scroll to focused element
      if (widget.enableAutoScroll && currentFocus != null) {
        _scrollToFocusedElement(currentFocus);
      }
      
      // Announce focus change
      if (widget.enableAccessibilityAnnouncements && currentFocus != null) {
        _announceFocusChange(currentFocus);
      }
      
      // Notify callback
      widget.onFocusChange?.call(currentFocus);
      
      // Debug logging
      if (widget.enableFocusDebugging) {
        _debugFocusChange(currentFocus);
      }
    }
  }

  void _storeFocus(FocusNode? node) {
    if (node != null && widget.restoreKey != null) {
      // Store focus information for restoration
      // This would typically use a service or shared preferences
      debugPrint('Storing focus for restoration: ${widget.restoreKey}');
    }
  }

  void _restoreFocus() {
    if (widget.restoreKey != null) {
      // Restore focus from stored information
      // This would typically retrieve from a service or shared preferences
      debugPrint('Restoring focus for: ${widget.restoreKey}');
    }
  }

  void _scrollToFocusedElement(FocusNode focusNode) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final context = focusNode.context;
      if (context != null) {
        Scrollable.ensureVisible(
          context,
          duration: DesignTokens.durationNormal,
          curve: Curves.easeInOut,
          alignmentPolicy: ScrollPositionAlignmentPolicy.keepVisibleAtEnd,
        );
      }
    });
  }

  void _announceFocusChange(FocusNode focusNode) {
    final context = focusNode.context;
    if (context != null) {
      final widget = context.widget;
      String announcement = _generateFocusAnnouncement(widget);
      
      if (announcement.isNotEmpty) {
        SemanticsService.announce(
          announcement,
          TextDirection.ltr,
        );
      }
    }
  }

  String _generateFocusAnnouncement(Widget widget) {
    // Generate appropriate announcement based on widget type
    if (widget is TextField || widget is TextFormField) {
      return 'Text field focused';
    } else if (widget is ElevatedButton || 
               widget is TextButton || 
               widget is OutlinedButton) {
      return 'Button focused';
    } else if (widget is Checkbox) {
      return 'Checkbox focused';
    } else if (widget is Radio) {
      return 'Radio button focused';
    } else if (widget is Switch) {
      return 'Switch focused';
    } else if (widget is Slider) {
      return 'Slider focused';
    } else if (widget is ListTile) {
      return 'List item focused';
    } else if (widget is Card) {
      return 'Card focused';
    } else if (widget is Tab) {
      return 'Tab focused';
    }
    
    return 'Element focused';
  }

  void _debugFocusChange(FocusNode? focusNode) {
    if (focusNode != null) {
      final context = focusNode.context;
      final widget = context?.widget;
      debugPrint(
        'Focus changed in ${this.widget.debugLabel ?? 'AccessibleFocusTraversalGroup'}: '
        '${widget.runtimeType} (${focusNode.debugLabel})'
      );
    }
  }

  bool _handleKeyEvent(KeyEvent event) {
    if (event is! KeyDownEvent) return false;
    
    final isShiftPressed = HardwareKeyboard.instance.isShiftPressed;
    final isCtrlPressed = HardwareKeyboard.instance.isControlPressed;
    final isAltPressed = HardwareKeyboard.instance.isAltPressed;
    
    // Handle skip links
    if (widget.enableSkipLinks && isAltPressed) {
      return _handleSkipLinkShortcuts(event);
    }
    
    // Handle directional navigation
    if (widget.enableDirectionalNavigation) {
      return _handleDirectionalNavigation(event, isShiftPressed);
    }
    
    // Handle focus trapping
    if (widget.enableFocusTrapping && _isTrapped) {
      return _handleFocusTrapping(event, isShiftPressed);
    }
    
    return false;
  }

  bool _handleSkipLinkShortcuts(KeyEvent event) {
    // Alt + 1-9 for skip links
    if (event.logicalKey.keyId >= LogicalKeyboardKey.digit1.keyId &&
        event.logicalKey.keyId <= LogicalKeyboardKey.digit9.keyId) {
      
      final index = event.logicalKey.keyId - LogicalKeyboardKey.digit1.keyId;
      final skipLinkKeys = widget.skipLinkTargets?.keys.toList();
      
      if (skipLinkKeys != null && index < skipLinkKeys.length) {
        final targetKey = skipLinkKeys[index];
        _activateSkipLink(targetKey);
        return true;
      }
    }
    
    return false;
  }

  bool _handleDirectionalNavigation(KeyEvent event, bool isShiftPressed) {
    final currentFocus = FocusScope.of(context).focusedChild;
    if (currentFocus == null) return false;
    
    switch (event.logicalKey) {
      case LogicalKeyboardKey.arrowUp:
        return _moveDirectionalFocus(TraversalDirection.up);
      case LogicalKeyboardKey.arrowDown:
        return _moveDirectionalFocus(TraversalDirection.down);
      case LogicalKeyboardKey.arrowLeft:
        return _moveDirectionalFocus(TraversalDirection.left);
      case LogicalKeyboardKey.arrowRight:
        return _moveDirectionalFocus(TraversalDirection.right);
      case LogicalKeyboardKey.home:
        return _moveToFirstFocusable();
      case LogicalKeyboardKey.end:
        return _moveToLastFocusable();
      default:
        return false;
    }
  }

  bool _handleFocusTrapping(KeyEvent event, bool isShiftPressed) {
    if (event.logicalKey == LogicalKeyboardKey.tab) {
      final currentFocus = FocusScope.of(context).focusedChild;
      if (currentFocus == null) return false;
      
      final focusableNodes = _getFocusableNodesInGroup();
      if (focusableNodes.isEmpty) return false;
      
      final currentIndex = focusableNodes.indexOf(currentFocus);
      if (currentIndex == -1) return false;
      
      int nextIndex;
      if (isShiftPressed) {
        nextIndex = currentIndex == 0 ? focusableNodes.length - 1 : currentIndex - 1;
      } else {
        nextIndex = currentIndex == focusableNodes.length - 1 ? 0 : currentIndex + 1;
      }
      
      focusableNodes[nextIndex].requestFocus();
      widget.onFocusTrapped?.call();
      return true;
    }
    
    return false;
  }

  bool _moveDirectionalFocus(TraversalDirection direction) {
    final currentFocus = FocusScope.of(context).focusedChild;
    if (currentFocus == null) return false;
    
    final policy = widget.policy ?? ReadingOrderTraversalPolicy();
    final next = policy.findFirstFocusInDirection(currentFocus, direction);
    
    if (next != null) {
      next.requestFocus();
      return true;
    }
    
    return false;
  }

  bool _moveToFirstFocusable() {
    final focusableNodes = _getFocusableNodesInGroup();
    if (focusableNodes.isNotEmpty) {
      focusableNodes.first.requestFocus();
      return true;
    }
    return false;
  }

  bool _moveToLastFocusable() {
    final focusableNodes = _getFocusableNodesInGroup();
    if (focusableNodes.isNotEmpty) {
      focusableNodes.last.requestFocus();
      return true;
    }
    return false;
  }

  List<FocusNode> _getFocusableNodesInGroup() {
    final scope = FocusScope.of(context);
    final descendants = scope.descendants.where((node) => 
      node.canRequestFocus && 
      node.context != null &&
      _isNodeInGroup(node)
    ).toList();
    
    final policy = widget.policy ?? ReadingOrderTraversalPolicy();
    return policy.sortDescendants(descendants, FocusScope.of(context)).toList();
  }

  bool _isNodeInGroup(FocusNode node) {
    // Check if the node belongs to this group
    BuildContext? context = node.context;
    while (context != null) {
      if (mounted && context == this.context) return true;
      final ancestorWidget = context.findAncestorWidgetOfExactType<AccessibleFocusTraversalGroup>();
      context = ancestorWidget != null ? context.findAncestorStateOfType<_AccessibleFocusTraversalGroupState>()?.context : null;
    }
    return false;
  }

  void _activateSkipLink(String targetKey) {
    final targetGlobalKey = widget.skipLinkTargets?[targetKey];
    if (targetGlobalKey?.currentContext != null) {
      final targetContext = targetGlobalKey!.currentContext!;
      
      // Find focusable widget in target
      final focusScope = FocusScope.of(targetContext);
      final focusableNode = focusScope.descendants.firstWhere(
        (node) => node.canRequestFocus,
        orElse: () => focusScope,
      );
      
      focusableNode.requestFocus();
      
      // Scroll to target
      if (widget.enableAutoScroll) {
        Scrollable.ensureVisible(
          targetContext,
          duration: DesignTokens.durationNormal,
          curve: Curves.easeInOut,
        );
      }
      
      // Announce skip link activation
      if (widget.enableAccessibilityAnnouncements) {
        SemanticsService.announce(
          'Skipped to $targetKey',
          TextDirection.ltr,
        );
      }
      
      widget.onSkipLinkActivated?.call(targetKey);
    }
  }

  void enableFocusTrapping() {
    setState(() {
      _isTrapped = true;
    });
  }

  void disableFocusTrapping() {
    setState(() {
      _isTrapped = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    final semanticColors = context.semanticColors;
    
    Widget child = widget.child;
    
    // Wrap with skip links if enabled
    if (widget.enableSkipLinks && widget.skipLinkTargets != null) {
      child = _buildWithSkipLinks(child);
    }
    
    // Wrap with focus debugging if enabled
    if (widget.enableFocusDebugging) {
      child = _buildWithFocusDebugging(child, semanticColors);
    }
    
    // Wrap with semantic information
    child = Semantics(
      label: widget.semanticLabel,
      container: true,
      explicitChildNodes: true,
      child: child,
    );
    
    // Wrap with focus traversal group
    child = FocusTraversalGroup(
      policy: widget.policy ?? _createDefaultPolicy(),
      descendantsAreFocusable: widget.descendantsAreFocusable,
      descendantsAreTraversable: widget.descendantsAreTraversable,
      child: child,
    );
    
    // Wrap with focus scope for group management
    child = FocusScope(
      node: _groupFocusNode,
      skipTraversal: widget.skipTraversal,
      child: child,
    );
    
    // Wrap with keyboard listener for custom navigation
    child = KeyboardListener(
      focusNode: _keyboardListenerFocusNode,
      onKeyEvent: _handleKeyEvent,
      child: child,
    );
    
    return child;
  }

  Widget _buildWithSkipLinks(Widget child) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Skip links (initially hidden, shown on focus)
        _SkipLinksWidget(
          targets: widget.skipLinkTargets!,
          onActivated: _activateSkipLink,
        ),
        Expanded(child: child),
      ],
    );
  }

  Widget _buildWithFocusDebugging(Widget child, SemanticColors semanticColors) {
    return Stack(
      children: [
        child,
        if (widget.enableFocusDebugging)
          Positioned(
            top: 0,
            right: 0,
            child: _FocusDebugOverlay(
              groupLabel: widget.debugLabel ?? 'Focus Group',
              currentFocus: _lastFocusedNode,
              focusHistory: _focusHistory,
              isTrapped: _isTrapped,
              semanticColors: semanticColors,
            ),
          ),
      ],
    );
  }

  FocusTraversalPolicy _createDefaultPolicy() {
    if (widget.focusOrder != null) {
      return _ManualOrderTraversalPolicy(widget.focusOrder!);
    }
    return ReadingOrderTraversalPolicy();
  }
}

/// Custom traversal policy for manual focus order
class _ManualOrderTraversalPolicy extends FocusTraversalPolicy {
  _ManualOrderTraversalPolicy(this.focusOrder);
  
  final List<GlobalKey> focusOrder;
  
  @override
  Iterable<FocusNode> sortDescendants(Iterable<FocusNode> descendants, FocusNode currentNode) {
    final orderedNodes = <FocusNode>[];
    final unorderedNodes = descendants.toList();
    
    // Add nodes in specified order
    for (final key in focusOrder) {
      final context = key.currentContext;
      if (context != null) {
        final focusScope = FocusScope.of(context);
        final node = focusScope.focusedChild ?? focusScope;
        if (unorderedNodes.contains(node)) {
          orderedNodes.add(node);
          unorderedNodes.remove(node);
        }
      }
    }
    
    // Add remaining nodes
    orderedNodes.addAll(unorderedNodes);
    
    return orderedNodes;
  }

  @override
  FocusNode? findFirstFocusInDirection(FocusNode currentNode, TraversalDirection direction) {
    // Use default implementation
    return null;
  }

  @override
  bool inDirection(FocusNode currentNode, TraversalDirection direction) {
    // Use default implementation
    return false;
  }
}

/// Skip links widget for efficient navigation
class _SkipLinksWidget extends StatelessWidget {
  const _SkipLinksWidget({
    required this.targets,
    required this.onActivated,
  });
  
  final Map<String, GlobalKey> targets;
  final ValueChanged<String> onActivated;
  
  @override
  Widget build(BuildContext context) {
    final semanticColors = context.semanticColors;
    
    return Offstage(
      offstage: true,
      child: Wrap(
        children: targets.keys.map((key) {
          return Padding(
            padding: const EdgeInsets.all(DesignTokens.space1),
            child: Focus(
              onFocusChange: (hasFocus) {
                // Show skip link when focused
              },
              child: ElevatedButton(
                onPressed: () => onActivated(key),
                style: ElevatedButton.styleFrom(
                  backgroundColor: semanticColors.info,
                  foregroundColor: semanticColors.onInfo,
                  padding: const EdgeInsets.symmetric(
                    horizontal: DesignTokens.space4,
                    vertical: DesignTokens.space2,
                  ),
                ),
                child: Text('Skip to $key'),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}

/// Focus debugging overlay
class _FocusDebugOverlay extends StatelessWidget {
  const _FocusDebugOverlay({
    required this.groupLabel,
    required this.currentFocus,
    required this.focusHistory,
    required this.isTrapped,
    required this.semanticColors,
  });
  
  final String groupLabel;
  final FocusNode? currentFocus;
  final List<FocusNode> focusHistory;
  final bool isTrapped;
  final SemanticColors semanticColors;
  
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(DesignTokens.space2),
      decoration: BoxDecoration(
        color: semanticColors.warningContainer.withOpacity(0.9),
        borderRadius: BorderRadius.circular(DesignTokens.radiusSm),
        border: Border.all(
          color: semanticColors.warning,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Focus Debug: $groupLabel',
            style: const TextStyle(
              fontSize: DesignTokens.fontSizeXs,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: DesignTokens.space1),
          Text(
            'Current: ${currentFocus?.debugLabel ?? 'None'}',
            style: const TextStyle(fontSize: DesignTokens.fontSizeXs),
          ),
          Text(
            'Trapped: $isTrapped',
            style: const TextStyle(fontSize: DesignTokens.fontSizeXs),
          ),
          Text(
            'History: ${focusHistory.length}',
            style: const TextStyle(fontSize: DesignTokens.fontSizeXs),
          ),
        ],
      ),
    );
  }
}

/// Extension methods for easy access to focus traversal functionality
extension AccessibleFocusTraversalGroupExtension on BuildContext {
  /// Find the nearest AccessibleFocusTraversalGroup
  _AccessibleFocusTraversalGroupState? get focusTraversalGroup {
    return findAncestorStateOfType<_AccessibleFocusTraversalGroupState>();
  }
  
  /// Enable focus trapping in the nearest group
  void enableFocusTrapping() {
    focusTraversalGroup?.enableFocusTrapping();
  }
  
  /// Disable focus trapping in the nearest group
  void disableFocusTrapping() {
    focusTraversalGroup?.disableFocusTrapping();
  }
}